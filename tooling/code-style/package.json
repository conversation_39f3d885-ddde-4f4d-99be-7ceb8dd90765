{"name": "@qantasexperiences/code-style", "description": "A consolidated package for managing all code style tools, including ESLint, Prettier, and Stylelint configurations.", "type": "module", "private": true, "version": "0.1.0", "exports": {"./eslint/base": "./eslint/base.js", "./eslint/nextjs": "./eslint/nextjs.js", "./eslint/react": "./eslint/react.js", "./prettier": "./prettier/prettier.mjs", "./stylelint": "./stylelint/config.mjs"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0"}, "dependencies": {"@cspell/dict-lorem-ipsum": "^4.0.4", "@cspell/eslint-plugin": "^8.17.5", "@eslint/js": "^9.23.0", "@next/eslint-plugin-next": "^15.3.1", "@stylistic/eslint-plugin": "^2.13.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.1.1", "eslint-config-turbo": "^2.4.4", "eslint-import-resolver-typescript": "^3.8.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-storybook": "^0.11.6", "eslint-plugin-typescript-sort-keys": "^3.3.0", "globals": "^15.14.0", "typescript-eslint": "^8.27.0"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@types/eslint": "^9.6.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "stylelint": "^16.13.2", "stylelint-config-standard": "^37.0.0", "typescript": "^5.7.3"}, "prettier": "./prettier/prettier.mjs"}
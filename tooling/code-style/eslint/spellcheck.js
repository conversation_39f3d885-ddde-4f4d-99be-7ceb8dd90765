import cspellConfigs from '@cspell/eslint-plugin/configs';

export const spellcheckConfig = {
  extends: [cspellConfigs.recommended],
  rules: {
    '@cspell/spellchecker': [
      'error',
      {
        cspell: {
          language: 'en-US, en-GB',
          dictionaries: ['lorem-ipsum'],
          words: [
            // Code
            'autodocs',
            'csstype',
            'unhover',
            'data-testid',
            'qantasexperiences',
            'qantashotels',
            'travelapi',
            'Groq',
            'pava',
            'embla',
            'ianvs',
            'ejas',
            'ctfassets',
            'autotitle',
            'generateslug',
            'describedby',
            'networkidle',
            'Storybookinitialised',
            'domcontentloaded',

            // Fonts
            'ciutadella',
            'neue',
            'roboto',

            // Brands
            'contentful',
            'jetstar',
            'adyen',

            // Random
            'ecommerce',
            'Highres',
            'Smartfill',
            'qlff',
            'sess',
            'checkin',
            'dismissable',
            'couldn',
            'dedede',
            'Fsrc',
            'SCHMGPYQFD',
            'QAPHWYGZ',
            'KWGRQM',
            'Interactable',
            'NCLS',
            'ensuite',
            'pillowtop',

            // Proper nouns
            'Tullamarine',
            'Haneda',
            'Hayman',
            'uluru',
            'shangri',
            'tagling',
            'shubham',
            'Melb',
            'Crowne',
            'Novotel',
            'Southbank',
            'Seminyak',

            // Timezones
            'AEDT',
            'AEST',

            // Third parties
            'iSeatz',
            'Optimizely',
          ],
        },
      },
    ],
  },
};

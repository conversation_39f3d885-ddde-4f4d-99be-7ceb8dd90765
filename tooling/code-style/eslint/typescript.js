import path from 'path';
import { fileURLToPath } from 'url';
import tseslint from 'typescript-eslint';

export const typescriptConfig = [
  {
    extends: [...tseslint.configs.recommendedTypeChecked, ...tseslint.configs.stylisticTypeChecked],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      parserOptions: {
        project: true,
        tsconfigRootDir: path.resolve(fileURLToPath(import.meta.url), '../../../../..'),
      },
    },
    rules: {
      /** TODO: Enable rules from apps/relationship config */
      // '@typescript-eslint/array-type': 'error',
      '@typescript-eslint/consistent-type-imports': 'error',
      /** TODO: Enable rules from apps/relationship config */
      // '@typescript-eslint/explicit-function-return-type': [
      //   'error',
      //   {
      //     allowExpressions: true,
      //   },
      // ],

      'default-param-last': 'off',
      '@typescript-eslint/default-param-last': 'error',

      'dot-notation': 'off',
      '@typescript-eslint/dot-notation': 'error',
      '@typescript-eslint/naming-convention': [
        'error',
        {
          selector: 'variable',
          format: ['camelCase', 'PascalCase', 'UPPER_CASE'],
        },
        {
          selector: 'variable',
          modifiers: ['destructured'],
          format: null,
        },
        {
          selector: 'function',
          format: ['camelCase', 'PascalCase'],
        },
        {
          selector: 'typeLike',
          format: ['PascalCase'],
        },
      ],
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_',
        },
      ],
      'no-empty-function': 'off',
      '@typescript-eslint/no-empty-function': 'error',

      '@typescript-eslint/no-unnecessary-condition': 'error',

      'no-loop-func': 'off',
      '@typescript-eslint/no-loop-func': 'error',

      'no-use-before-define': 'off',
      '@typescript-eslint/no-use-before-define': 'error',

      '@typescript-eslint/prefer-nullish-coalescing': [
        'error',
        {
          ignoreBooleanCoercion: true,
          ignoreConditionalTests: true,
        },
      ],

      '@typescript-eslint/no-non-null-asserted-nullish-coalescing': 'error',
      '@typescript-eslint/no-non-null-assertion': 'error',

      'no-shadow': 'off',
      '@typescript-eslint/no-shadow': 'error',
    },
  },
];

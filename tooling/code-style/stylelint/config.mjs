/** @type {import('stylelint').Config} */
export default {
  extends: ['stylelint-config-standard'],
  rules: {
    'alpha-value-notation': 'number',
    'at-rule-no-deprecated': [
      true,
      {
        severity: 'warning',
      },
    ],
    'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: [
          // tailwind @
          'apply',
          'responsive',
          'screen',
          'tailwind',
          'variants',
        ],
      },
    ],
    'color-function-notation': 'legacy',
    'color-hex-length': 'long',
    'import-notation': 'string',
    'value-keyword-case': [
      'lower',
      {
        camelCaseSvgKeywords: true,
      },
    ],
  },
};

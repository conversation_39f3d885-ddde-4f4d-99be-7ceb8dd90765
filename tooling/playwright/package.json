{"name": "@qantasexperiences/playwright-config", "private": true, "version": "0.1.0", "type": "module", "exports": {"./config": "./config.ts", "./helpers": "./helpers.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit"}, "devDependencies": {"@playwright/test": "1.51.0", "@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@types/node": "^22.10.6", "typescript": "^5.7.3"}, "prettier": "@qantasexperiences/code-style/prettier"}
import type { Config } from 'jest';

const TRANSFORM_IGNORED = [
  '@t3-oss/env-nextjs',
  '@t3-oss/env-core',
  'ky',
  'camelcase-keys',
  'map-obj',
  'camelcase',
  'quick-lru',
  'uuid',
].join('|');

const config: Config = {
  maxWorkers: 3,
  transform: {
    '.*\\.((t|j)sx?)$': [
      '@swc/jest',
      {
        jsc: {
          transform: {
            react: {
              runtime: 'automatic',
            },
          },
        },
      },
    ],
  },
  moduleNameMapper: {
    '^~/(.*)$': '<rootDir>/src/$1',
    '^.+\\.(png)$': require.resolve(`./__mocks__/fileMock.js`),
    '^.+\\.(svg)$': require.resolve(`./__mocks__/svgMock.js`),
    '^.+\\.(css)$': require.resolve(`./__mocks__/styleMock.js`),
    '^(.+\\.svg)(\\?\\w+)$': require.resolve(`./__mocks__/fileMock.js`),
    '@sentry/nextjs': '<rootDir>/node_modules/@sentry/nextjs',
  },
  transformIgnorePatterns: [
    `/node_modules/(?!.pnpm)(?!(${TRANSFORM_IGNORED})/)`,
    `/node_modules/.pnpm/(?!(${TRANSFORM_IGNORED.replace(/\//g, '\\+')})@)`,
  ],
  setupFilesAfterEnv: [
    require.resolve('./dot-env.setup.ts'),
    'jest-localstorage-mock',
    './jest.setup.ts',
  ],
  testEnvironment: require.resolve(`./environment.ts`),
  testEnvironmentOptions: {
    customExportConditions: ['msw'], // See https://github.com/mswjs/msw/issues/1786
  },
  testRegex: '.*\\.test.((t|j)sx?)$',
  passWithNoTests: true,
};

export default config;

{"name": "@qantasexperiences/jest", "private": true, "type": "commonjs", "version": "0.1.0", "exports": {"./config": "./config.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit"}, "devDependencies": {"@jest/environment": "^29.7.0", "@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@swc/core": "^1.10.7", "@swc/jest": "^0.2.37", "@types/jest": "^29.5.14", "dotenv": "^16.4.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-localstorage-mock": "^2.4.26", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "prettier": "@qantasexperiences/code-style/prettier"}
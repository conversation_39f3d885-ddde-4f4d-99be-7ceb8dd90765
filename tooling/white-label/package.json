{"name": "@qantasexperiences/white-label", "private": true, "type": "module", "scripts": {"stop": "pm2 delete proxy", "proxy": "pm2 start --name proxy proxy/index.cjs", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern"}, "exports": {"./pm2": "./pm2/index.cjs"}, "devDependencies": {"@qantasexperiences/code-style": "workspace:*", "http-proxy": "^1.18.1", "pm2": "^5.4.3"}, "prettier": "@qantasexperiences/code-style/prettier"}
global
  maxconn       4000
  tune.bufsize  32768

defaults
  mode        http
  option      http-server-close
  option      forwardfor 
  option      redispatch
  retries     3
  timeout     connect 25000ms 
  timeout     client 60000ms 
  timeout     server 60000ms
  timeout     queue 60000ms
  timeout     http-request 15000ms
  timeout     http-keep-alive 15000ms
  log         global
  option      httplog
  log         stdout format raw daemon            # send everything to stdout

resolvers aws
  nameserver  dns ***************:53
  hold valid  30s

frontend http
  bind 0.0.0.0:8080

  capture request header Referer len 128
  capture request header User-Agent len 256
  capture request header True-Client-IP len 64

  compression algo gzip
  compression type text/html text/plain text/javascript application/javascript application/xml text/css application/json
  

  acl host_staging hdr(host) -i staging-qp-customer-ui.qantashotels.com
  acl host_sit hdr(host) -i sit-qp-customer-ui.qantashotels.com

  acl path_consider path_beg /holidays/search/list
  acl path_consider_api path_beg /holidays/consider-api

  use_backend consider if host_staging path_consider
  use_backend consider if host_sit path_consider
  use_backend consider-api if host_staging path_consider_api
  use_backend consider-api if host_sit path_consider_api
  
  # add paths for pages in development
  # acl path_inspire_non_prod path_beg ***
  
  # inspire app paths
  acl path_inspire path_beg /holidays/activities
  acl path_inspire_api path_beg /holidays/inspire-api
  
  # inspire app non-prod paths
  # use_backend inspire if host_staging path_inspire_non_prod
  # use_backend inspire if host_sit path_inspire_non_prod
  
  # inspire app prod paths
  use_backend inspire-api if path_inspire_api
  use_backend inspire if path_inspire

  default_backend       holidays_ui

  monitor-uri /health-check

backend inspire
  server inspire $INSPIRE_INTERNAL_ALB ssl verify none resolvers aws
  
  http-request set-path /qantas%[path]
  http-response replace-value Location ^https://([^/]+)(/qantas)(.*)$ https://\1\3

  # Add csp policies
  http-response add-header Content-Security-Policy %[str(defaultcsp),map(/usr/local/etc/haproxy/haproxy.eja.csp-policy)]

backend inspire-api
 server inspire $INSPIRE_INTERNAL_ALB ssl verify none resolvers aws
 http-request set-path %[path,regsub(/holidays/inspire-api,/api)]

backend consider
  server consider $CONSIDER_INTERNAL_ALB ssl verify none resolvers aws

  http-request set-path /qantas%[path]
  http-response replace-value Location ^https://([^/]+)(/qantas)(.*)$ https://\1\3

  # Add csp policies
  http-response add-header Content-Security-Policy %[str(defaultcsp),map(/usr/local/etc/haproxy/haproxy.eja.csp-policy)]

backend consider-api
  server consider $CONSIDER_INTERNAL_ALB ssl verify none resolvers aws
  http-request set-path %[path,regsub(/holidays/consider-api,/api)]
  
backend holidays_ui
  server ui $UI_INTERNAL_ALB ssl verify none resolvers aws
  
  # Add csp policies
  http-response add-header Content-Security-Policy %[str(defaultcsp),map(/usr/local/etc/haproxy/haproxy.legacy.csp-policy)]

# CI Build-Time Dockerfile (Builder usage: to run QE and Build any EJA packages/apps)
# Build from node builder image
FROM 730011650125.dkr.ecr.ap-southeast-2.amazonaws.com/base/node:20.9.0-builder-144 AS builder

# Switch to 'root' user
USER root

# Install Chromium and necessary dependencies for testing
RUN apt-get update && apt-get install -y \
  chromium \
  ca-certificates \
  fonts-liberation \
  libappindicator3-1 \
  libasound2 \
  libatk-bridge2.0-0 \
  libatk1.0-0 \
  libcups2 \
  libdbus-1-3 \
  libnss3 \
  libxcomposite1 \
  libxdamage1 \
  libxrandr2 \
  xdg-utils \
  xvfb \
  xauth \
  --no-install-recommends \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# Set /pnpm to the PATH and enable Corepack
ENV PATH="/pnpm:$PATH"
RUN corepack enable

# Set working directory
WORKDIR /application

# Switch to the existing 'hooroo' user
USER hooroo

# Copy .npmrc, package.json, pnpm-lock.yaml, pnpm-workspace.yaml, and turbo.json for better caching
COPY --chown=hooroo:hooroo .npmrc package.json pnpm-lock.yaml pnpm-workspace.yaml turbo.json ./

# Load packages into the virtual store for docker layering and pnpm installation cache
RUN pnpm fetch

# Copy all project files except those listed on `.dockerignore` (including Next.js app, packages, etc.)
COPY --chown=hooroo:hooroo . .

# Install all dependencies from pnpm virtual store first, but still allowing `pnpm dlx` hotloads
RUN pnpm install --prefer-offline

# Install Playwright Chromium browser (remove 'chromium' when cross-browser testing is ready)
# Pinned to version 1.51.0 to match the workspace packages
RUN pnpm dlx playwright@1.51.0 install chromium

# Set empty default command for reference
CMD []

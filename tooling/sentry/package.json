{"name": "@qantasexperiences/sentry", "private": true, "version": "0.1.0", "type": "module", "exports": {"./client": "./client.ts", "./edge": "./edge.ts", "./server": "./server.ts", "./instrumentation": "./instrumentation.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit"}, "peerDependencies": {"@sentry/nextjs": "^8.34.0"}, "devDependencies": {"@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "typescript": "^5.7.3"}, "prettier": "@qantasexperiences/code-style/prettier"}
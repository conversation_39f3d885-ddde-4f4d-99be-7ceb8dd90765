import * as Sentry from '@sentry/nextjs';

type DSN = string | undefined;

export const sentryClientConfig = (dsn: DSN) => {
  return Sentry.init({
    dsn,

    // Adjust this value in production, or use tracesSampler for greater control
    tracesSampleRate: 1,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: false,

    replaysOnErrorSampleRate: 1.0,

    // This sets the sample rate to be 10%. You may want this to be 100% while
    // in development and sample at a lower rate in production
    replaysSessionSampleRate: 0.1,

    // You can remove this option if you're not planning to use the Sentry Session Replay feature:
    // Note: Commented out until replay quota is increased
    // integrations: [
    //   Sentry.replayIntegration({
    //     // Additional SDK configuration goes in here, for example:
    //     maskAllText: true,
    //     blockAllMedia: true,
    //   }),
    // ],
  });
};

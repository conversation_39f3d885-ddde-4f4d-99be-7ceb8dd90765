import * as Sentry from '@sentry/nextjs';

type DSN = string | undefined;

export const sentryServerConfig = (dsn: DSN) => {
  return Sentry.init({
    dsn,

    // Adjust this value in production, or use tracesSampler for greater control
    tracesSampleRate: 1,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: false,
  });
};

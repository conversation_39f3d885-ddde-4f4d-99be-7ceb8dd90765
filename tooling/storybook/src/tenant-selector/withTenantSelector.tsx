import type { Decorator } from '@storybook/react';
import type { ForwardedRef, ReactNode } from 'react';
import { forwardRef, useState } from 'react';

import type { Brand } from '@qantasexperiences/tenants';
import { TenantProvider, useTenant } from '@qantasexperiences/tenants/react';
import { GlobalPortalContainerProvider } from '@qantasexperiences/ui/providers';

import { getGlobalValues } from './utils';

const SideBySide = ({ children }: { children: ReactNode }) => {
  return <div className="flex w-full flex-wrap justify-around gap-2 sm:gap-8">{children}</div>;
};

const GlobalPortalRoot = forwardRef((_, ref: ForwardedRef<HTMLDivElement>) => {
  return <div className="absolute left-0 right-0 top-0 h-0" ref={ref}></div>;
});

const DataTheme = ({ children, theme }: { children: ReactNode; theme: Brand }) => {
  return (
    <div
      data-theme={theme}
      className="flex max-w-full shrink grow basis-auto items-center justify-center"
    >
      {children}
    </div>
  );
};

const BrandWrapper = ({ children }: { children: ReactNode }) => {
  const { brand, channel } = useTenant();
  const [qantasRef, setQantasRef] = useState<HTMLDivElement | null>(null);
  const [jetstarRef, setJetstarRef] = useState<HTMLDivElement | null>(null);
  const [singleBrandRef, setSingleBrandRef] = useState<HTMLDivElement | null>(null);

  // @ts-expect-error could be side-by-side
  if (brand === 'side-by-side') {
    return (
      <SideBySide>
        <DataTheme theme="qantas">
          <GlobalPortalContainerProvider portalContainer={qantasRef}>
            <GlobalPortalRoot ref={setQantasRef} />
            <TenantProvider value={{ brand: 'qantas', channel }}>{children}</TenantProvider>
          </GlobalPortalContainerProvider>
        </DataTheme>
        <DataTheme theme="jetstar">
          <GlobalPortalRoot ref={setJetstarRef} />
          <GlobalPortalContainerProvider portalContainer={jetstarRef}>
            <TenantProvider value={{ brand: 'jetstar', channel }}>{children}</TenantProvider>
          </GlobalPortalContainerProvider>
        </DataTheme>
      </SideBySide>
    );
  }

  return (
    <DataTheme theme={brand}>
      <GlobalPortalRoot ref={setSingleBrandRef} />
      <GlobalPortalContainerProvider portalContainer={singleBrandRef}>
        {children}
      </GlobalPortalContainerProvider>
    </DataTheme>
  );
};

const ChannelWrapper = ({ children }: { children: ReactNode }) => {
  const { brand, channel } = useTenant();

  // @ts-expect-error could be side-by-side
  if (channel === 'side-by-side') {
    return (
      <SideBySide>
        <TenantProvider value={{ channel: 'hotels', brand }}>{children}</TenantProvider>
        <TenantProvider value={{ channel: 'holidays', brand }}>{children}</TenantProvider>
      </SideBySide>
    );
  }

  return children;
};

const withTenantSelector: Decorator = (Story, context) => {
  const { brand, channel } = getGlobalValues(context);

  return (
    // @ts-expect-error values could be 'side-by-side' but that is handled in other wrappers
    <TenantProvider value={{ brand, channel }}>
      <BrandWrapper>
        <ChannelWrapper>
          <Story />
        </ChannelWrapper>
      </BrandWrapper>
    </TenantProvider>
  );
};

export default withTenantSelector;

import type { Preview } from '@storybook/react';
import { createElement, useEffect, useState } from 'react';

import './globals.css';
import '@qantasexperiences/theming/styles/global.css';

import type { DocsContainerProps } from '@storybook/addon-docs';
import { DocsContainer } from '@storybook/addon-docs';
import { addons } from '@storybook/preview-api';
import { themes } from '@storybook/theming';
import { DARK_MODE_EVENT_NAME } from 'storybook-dark-mode';

import { withExperimentalDecorator } from './experimental-decorator';
import { tenantThemeGlobalTypes, withTenantSelector } from './tenant-selector';

const channel = addons.getChannel();

export const parameters = {
  darkMode: {
    darkClass: 'dark',
    lightClass: 'light',
  },
};

const preview: Preview = {
  globalTypes: {
    ...tenantThemeGlobalTypes,
  },
  parameters: {
    docs: {
      container: (props: DocsContainerProps) => {
        // Docs styling is independent of storybook, so must manually apply theming when dark mode event is fired
        /** TODO: FIX ME - disabled during Eslint v9 upgrade */
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isDark, setDark] = useState(document.documentElement.classList.contains('dark'));
        /** TODO: FIX ME - disabled during Eslint v9 upgrade */
        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          channel.on(DARK_MODE_EVENT_NAME, setDark);
          return () => channel.off(DARK_MODE_EVENT_NAME, setDark);
          /** TODO: FIX ME - disabled during Eslint v9 upgrade */
          // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [channel, setDark]);
        const currentProps = { ...props };
        currentProps.theme = isDark ? themes.dark : themes.light;
        return createElement(DocsContainer, currentProps);
      },
    },
    options: {
      storySort: {
        method: 'alphabetical',
        order: [
          'Introduction',
          'Assets',
          'Page',
          'Actions',
          'Layout and structure',
          'Selection and input',
          'Images and icons',
          'Lists and visual elements',
          'Feedback indicators',
          'Navigation',
          'Overlays',
          'Tables',
          'Experimental',
          'Components',
        ],
      },
    },
    layout: 'centered',
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        {
          name: 'light',
          value: '#fff',
        },
        {
          name: 'dark',
          value: '#222',
        },
        {
          name: 'grey',
          value: '#e0e0e0',
        },
      ],
    },
  },
  decorators: [withTenantSelector, withExperimentalDecorator],
  tags: ['autodocs'],
};

export default preview;

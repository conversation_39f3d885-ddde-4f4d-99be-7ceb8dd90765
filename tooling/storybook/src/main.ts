import { dirname, join } from 'path';
import type { StorybookConfig } from '@storybook/nextjs';

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string): string {
  return dirname(require.resolve(join(value, 'package.json')));
}

const propAllowList = new Set(['className']);
const propExcludeList = new Set([
  /* This is to hide tw prop showing up in all our component prop tables. It is being added
   * due to an experimental global type config from Next. */
  'tw',
]);

const config: StorybookConfig = {
  stories: [
    './*.mdx',
    '../../../apps/consider/src/**/*.stories.@(js|jsx|mjs|ts|tsx)',
    '../../../apps/consider/src/**/*.mdx',
    '../../../apps/inspire/src/**/*.stories.@(js|jsx|mjs|ts|tsx)',
    '../../../apps/inspire/src/**/*.mdx',
    '../../../apps/relationship/src/**/*.stories.@(js|jsx|mjs|ts|tsx)',
    '../../../apps/relationship/src/**/*.mdx',
    '../../../packages/ui/src/**/*.mdx',
    '../../../packages/ui/src/**/*.stories.@(js|jsx|mjs|ts|tsx)',
  ],
  addons: [
    getAbsolutePath('@storybook/addon-onboarding'),
    getAbsolutePath('@storybook/addon-links'),
    getAbsolutePath('@storybook/addon-essentials'),
    getAbsolutePath('@chromatic-com/storybook'),
    getAbsolutePath('@storybook/addon-interactions'),
    getAbsolutePath('@storybook/addon-themes'),
    getAbsolutePath('@storybook/addon-a11y'),
    getAbsolutePath('storybook-dark-mode'),
  ],
  framework: {
    name: getAbsolutePath('@storybook/nextjs'),
    options: {
      builder: {
        useSWC: true,
      },
    },
  },
  /* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-return, @typescript-eslint/no-shadow */
  /* https://react-svgr.com/docs/next/ */
  webpackFinal: (config: any) => {
    // @ts-expect-error ignoring
    const fileLoaderRule = config.module.rules.find((rule) => rule.test?.test?.('.svg'));

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: {
          not: fileLoaderRule.resourceQuery ? [...fileLoaderRule.resourceQuery.not, /url/] : /url/,
        }, // exclude if *.svg?url
        use: ['@svgr/webpack'],
      },
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
  /* eslint-enable @typescript-eslint/no-explicit-any */
  staticDirs: [{ from: '../../../packages/theming/src/fonts/', to: 'fonts' }],
  typescript: {
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      shouldExtractLiteralValuesFromEnum: true,
      shouldRemoveUndefinedFromOptional: true,
      propFilter: (prop) => {
        if (propAllowList.has(prop.name)) {
          return true;
        }

        const isExcluded = propExcludeList.has(prop.name);
        const isAriaProp = prop.name.startsWith('aria-');
        const isBaseReactType = !!prop.parent?.fileName.includes('types-react');

        return !isBaseReactType && !isAriaProp && !isExcluded;
      },
    },
  },
};
export default config;

{"name": "@qantasexperiences/storybook", "version": "0.1.0", "private": true, "type": "module", "exports": {".": "./index.ts"}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "typecheck": "tsc --noEmit", "storybook": "storybook dev -p 6006 -c src", "build-storybook": "storybook build -c src"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.3", "@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/tailwind-config": "workspace:*", "@qantasexperiences/tenants": "workspace:*", "@qantasexperiences/theming": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@qantasexperiences/ui": "workspace:*", "@qantasexperiences/utils": "workspace:*", "@storybook/addon-a11y": "^8.4.7", "@storybook/addon-docs": "^8.4.7", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/addon-onboarding": "^8.4.7", "@storybook/addon-themes": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/manager-api": "^8.4.7", "@storybook/nextjs": "^8.4.7", "@storybook/preview-api": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/test": "^8.4.7", "@storybook/theming": "^8.4.7", "@svgr/webpack": "^8.1.0", "@types/node": "^22.10.6", "@types/react": "npm:types-react@19.0.0-rc.1", "prettier": "^3.4.2", "react": "19.0.0-rc-77b637d6-20241016", "storybook": "^8.4.7", "storybook-dark-mode": "^4.0.2", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}, "overrides": {"@types/react": "npm:types-react@19.0.0-rc.1"}, "prettier": "@qantasexperiences/code-style/prettier"}
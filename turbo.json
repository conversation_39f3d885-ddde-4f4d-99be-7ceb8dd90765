{"$schema": "https://turborepo.org/schema.json", "globalDependencies": ["**/.env"], "globalEnv": ["BASE_PATH", "CI", "BRAND", "CHANNEL", "PLATFORM", "UPDATE_SNAPSHOTS", "DEBUG_MODE", "ALLOW_SIZE_MISMATCH"], "tasks": {"topo": {"dependsOn": ["^topo"]}, "build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "next-env.d.ts", "dist/**", ".output/**", ".vercel/output/**"]}, "delete": {"dependsOn": ["@qantasexperiences/white-label#stop", "^delete"], "cache": false}, "dev": {"dependsOn": ["@qantasexperiences/white-label#proxy", "^dev"], "cache": false}, "format:check": {"outputs": ["node_modules/.cache/.prettiercache"], "outputLogs": "new-only"}, "lint:css": {"dependsOn": ["topo"], "outputs": ["node_modules/.cache/.stylelintcache"]}, "lint": {"dependsOn": ["^build"], "outputs": ["node_modules/.cache/.eslintcache"]}, "lint:ci": {"dependsOn": ["^build"], "cache": false}, "typecheck": {"dependsOn": ["^build"], "outputs": ["node_modules/.cache/tsbuildinfo.json"]}, "clean": {"cache": false}, "test:unit": {"dependsOn": ["^build"], "outputs": ["node_modules/.cache/.jestcache"]}, "test:unit:ci": {"dependsOn": ["^build"], "outputs": ["node_modules/.cache/.jestcache"]}, "test:unit:watch": {"dependsOn": ["^build"], "outputs": ["node_modules/.cache/.jestcache"], "cache": false}, "test:high-level": {"dependsOn": ["^build"]}, "test:high-level:ci": {"dependsOn": ["^build"]}, "test:acceptance": {"dependsOn": ["^build"]}, "test:e2e": {"dependsOn": ["^build"]}, "storybook": {"persistent": true, "cache": false}, "build-storybook": {"dependsOn": ["^build"], "outputs": ["storybook-static"]}, "test:pa11y": {}}, "globalPassThroughEnv": ["BUILD_ID", "BUILDKITE_BRANCH", "BUILDKITE_BUILD_AUTHOR", "BUILDKITE_BUILD_NUMBER", "BUILDKITE_BUILD_URL", "ENVIRONMENT", "NEXT_PUBLIC_ENVIRONMENT", "NEXT_RUNTIME", "NODE_ENV", "npm_lifecycle_event"]}
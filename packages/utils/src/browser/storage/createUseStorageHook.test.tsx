import { act, renderHook } from '@testing-library/react';

import { useLocalStorage } from '../localStorage';
import { useSessionStorage } from '../sessionStorage';

describe.each(['useLocalStorage', 'useSessionStorage'])('%s', (hook) => {
  const store = (hook.charAt(3).toLowerCase() + hook.slice(4)) as 'localStorage' | 'sessionStorage';

  beforeAll(() => {
    // suppress console being cluttered by expected error
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  beforeEach(() => {
    window[store].clear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  it('initial state is the returned value', () => {
    const [key, initialValue] = ['test_key', 'test_value'] as const;
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    expect(result.current[0]).toBe(initialValue);
  });

  it('returns initialValue if storage item is invalid', () => {
    const [key, initialValue] = ['test_key', 'test_value'];
    window[store].setItem(key, '[object Object]');
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    expect(result.current[0]).toBe(initialValue);
  });

  it('updating state updates the returned value', () => {
    const [key, initialValue] = ['test_key', 'test_value'];
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    const newValue = 'updated_value';
    act(() => {
      const setState = result.current[1];
      setState(newValue);
    });

    expect(result.current[0]).toBe(newValue);
  });

  it(`updating state writes updates to ${store}`, () => {
    const [key, initialValue] = ['test_key', 'test_value'];
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    const newValue = 'updated_value';
    act(() => {
      const setState = result.current[1];
      setState(newValue);
    });

    expect(window[store].getItem(key)).toBe(JSON.stringify(newValue));
  });

  it(`setting state to null removes the ${store} key`, () => {
    const [key, initialValue] = ['test_key', 'test_value'];
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    act(() => {
      const setState = result.current[1];
      setState(null);
    });

    expect(window[store].getItem(key)).toBeNull();
    // expect the state to match the default value
    expect(result.current[0]).toBe(initialValue);
  });

  it(`setting state to undefined removes the ${store} key`, () => {
    const [key, initialValue] = ['test_key', 'test_value'];
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    act(() => {
      const setState = result.current[1];
      setState(undefined);
    });

    expect(window[store].getItem(key)).toBeNull();
    // expect the state to match the default value
    expect(result.current[0]).toBe(initialValue);
  });

  it('updating state with a callback function updates the returned value', () => {
    const [key, initialValue] = ['test_key', 2];
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    act(() => {
      const setState = result.current[1];
      setState((prev) => prev + 1);
    });

    expect(result.current[0]).toBe(3);
    expect(window[store].getItem(key)).toBe('3');
  });

  it('updating state updates multiple instances with the same key', () => {
    const [key, value] = ['test_key', 'test_value'];
    const [oKey, oValue] = ['other_key', 'other_value'];
    const { result: A } = renderHook(() =>
      hook === 'useLocalStorage' ? useLocalStorage(key, value) : useSessionStorage(key, value),
    );
    const { result: B } = renderHook(() =>
      hook === 'useLocalStorage' ? useLocalStorage(key, value) : useSessionStorage(key, value),
    );
    const { result: C } = renderHook(() =>
      hook === 'useLocalStorage' ? useLocalStorage(oKey, oValue) : useSessionStorage(oKey, oValue),
    );

    const updatedValue = 'updated_value';
    act(() => {
      const setState = A.current[1];
      setState(updatedValue);
    });

    expect(B.current[0]).toBe(updatedValue);
    expect(C.current[0]).toBe(oValue);
  });

  it('updating state only updates instances with the same key', () => {
    let renderCount = 0;
    const { result: A } = renderHook(() => {
      renderCount++;
      return hook === 'useLocalStorage'
        ? useLocalStorage('key_1', {})
        : useSessionStorage('key_1', {});
    });
    const { result: B } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage('key_2', 'initial')
        : useSessionStorage('key_2', 'initial'),
    );

    act(() => {
      const setStateA = A.current[1];
      setStateA({ a: 1 });
    });

    const count = renderCount;

    act(() => {
      const setStateB = B.current[1];
      setStateB('updated');
    });

    expect(renderCount).toBe(count);
  });

  it('serializes and deserializes string state correctly', () => {
    const [key, initialValue] = ['test_key', 'some string'];
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    expect(result.current[0]).toBe(initialValue);

    const updatedValue = 'a different string';
    act(() => {
      const setState = result.current[1];
      setState(updatedValue);
    });

    expect(window[store].getItem(key)).toBe(JSON.stringify(updatedValue));
    expect(result.current[0]).toBe(updatedValue);
  });

  it('serializes and deserializes number state correctly', () => {
    const [key, initialValue] = ['test_key', 523];
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    expect(result.current[0]).toBe(initialValue);

    const updatedValue = 100;
    act(() => {
      const setState = result.current[1];
      setState(updatedValue);
    });

    expect(window[store].getItem(key)).toBe(JSON.stringify(updatedValue));
    expect(result.current[0]).toBe(updatedValue);
  });

  it('serializes and deserializes array state correctly', () => {
    const [key, initialValue] = ['test_key', ['test_value', 'tv2']];
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    expect(result.current[0]).toEqual(initialValue);

    const updatedValue = ['different', 'array'];
    act(() => {
      const setState = result.current[1];
      setState(updatedValue);
    });

    expect(window[store].getItem(key)).toBe(JSON.stringify(updatedValue));
    expect(result.current[0]).toEqual(updatedValue);
  });

  it('serializes and deserializes object state correctly', () => {
    const [key, initialValue] = ['test_key', { tv1: 't', tv2: 'v' }];
    const { result } = renderHook(() =>
      hook === 'useLocalStorage'
        ? useLocalStorage(key, initialValue)
        : useSessionStorage(key, initialValue),
    );

    expect(result.current[0]).toEqual(initialValue);

    const updatedValue = { tv1: 'v', tv2: 't' };
    act(() => {
      const setState = result.current[1];
      setState(updatedValue);
    });

    expect(window[store].getItem(key)).toBe(JSON.stringify(updatedValue));
    expect(result.current[0]).toEqual(updatedValue);
  });

  describe('with custom stringify and parse functions', () => {
    const stringify = (value: Date) => value.toISOString();
    const parse = (value: string) => new Date(value);

    const key = 'test_key';
    const initialValue = new Date('2001-01-01');
    const newValue = new Date('2007-07-07');

    it('initial state is the returned value', () => {
      const { result } = renderHook(() =>
        hook === 'useLocalStorage'
          ? useLocalStorage(key, initialValue, { stringify, parse })
          : useSessionStorage(key, initialValue, { stringify, parse }),
      );

      expect(result.current[0]).toStrictEqual(initialValue);
    });

    it('updating state updates the returned value', () => {
      const { result } = renderHook(() =>
        hook === 'useLocalStorage'
          ? useLocalStorage(key, initialValue, { stringify, parse })
          : useSessionStorage(key, initialValue, { stringify, parse }),
      );

      act(() => {
        const setState = result.current[1];
        setState(newValue);
      });

      expect(result.current[0]).toStrictEqual(newValue);
    });

    it(`updating state writes updates to ${store}`, () => {
      const { result } = renderHook(() =>
        hook === 'useLocalStorage'
          ? useLocalStorage(key, initialValue, { stringify, parse })
          : useSessionStorage(key, initialValue, { stringify, parse }),
      );

      act(() => {
        const setState = result.current[1];
        setState(newValue);
      });

      expect(window[store].getItem(key)).toBe(stringify(newValue));
    });

    it.each([null, undefined])(`setting state to %s removes the ${store} key`, (updatedValue) => {
      const { result } = renderHook(() =>
        hook === 'useLocalStorage'
          ? useLocalStorage(key, initialValue, { stringify, parse })
          : useSessionStorage(key, initialValue, { stringify, parse }),
      );

      act(() => {
        const setState = result.current[1];
        setState(updatedValue);
      });

      expect(window[store].getItem(key)).toBeNull();
      // expect the state to match the default value
      expect(result.current[0]).toBe(initialValue);
    });

    it('updating state with a callback function updates the returned value', () => {
      const { result } = renderHook(() =>
        hook === 'useLocalStorage'
          ? useLocalStorage(key, initialValue, { stringify, parse })
          : useSessionStorage(key, initialValue, { stringify, parse }),
      );

      act(() => {
        const setState = result.current[1];
        setState((prev) => {
          const newDate = new Date(prev);
          newDate.setMonth(prev.getMonth() + 1);

          return newDate;
        });
      });

      const expectedDate = new Date('2001-02-01');

      expect(result.current[0]).toStrictEqual(expectedDate);
      expect(window[store].getItem(key)).toBe(stringify(expectedDate));
    });

    it('updating state updates multiple instances with the same key', () => {
      const [oKey, oValue] = ['other_key', 'other_value'];
      const { result: A } = renderHook(() =>
        hook === 'useLocalStorage'
          ? useLocalStorage(key, initialValue, {
              parse,
              stringify,
            })
          : useSessionStorage(key, initialValue, { parse, stringify }),
      );
      const { result: B } = renderHook(() =>
        hook === 'useLocalStorage'
          ? useLocalStorage(key, initialValue, {
              parse,
              stringify,
            })
          : useSessionStorage(key, initialValue, { parse, stringify }),
      );
      const { result: C } = renderHook(() =>
        hook === 'useLocalStorage'
          ? useLocalStorage(oKey, oValue)
          : useSessionStorage(oKey, oValue),
      );

      act(() => {
        const setState = A.current[1];
        setState(newValue);
      });

      expect(B.current[0]).toStrictEqual(newValue);
      expect(C.current[0]).toBe(oValue);
    });
  });
});

import type { EmblaCarouselType } from 'embla-carousel';
import useEmblaCarousel from 'embla-carousel-react';
import * as ReactAriaComponents from 'react-aria-components';

import { fireEvent, render, screen, userEvent } from '../../../../test-utils';
import { arrowVariants } from '../../../Navigation/components/Carousel/components/Arrow';
import { listVariants } from '../../../Navigation/components/List';
import { Tabs } from '../../Tabs';
import { TabList } from './TabList';

const tenant = { brand: 'qantas', channel: 'hotels' } as const;

const items = [
  { id: '1', title: 'Tab 1' },
  { id: '2', title: 'Tab 2' },
];

const element = document.createElement('div');
const slideNodes = [element, element, element, element];

jest.mock('embla-carousel-react');
const emblaRef = jest.fn();
const emblaApi = {
  slideNodes: () => slideNodes,
  canScrollNext: jest.fn(),
  canScrollPrev: jest.fn(),
  scrollNext: jest.fn(),
  scrollPrev: jest.fn(),
  on: jest.fn().mockReturnThis(),
  internalEngine: jest.fn().mockReturnValue({
    options: {},
    slideRegistry: [
      [0, 1],
      [2, 3],
    ],
    scrollBody: { useDuration: jest.fn() },
    scrollTo: { index: jest.fn() },
  }),
} as unknown as EmblaCarouselType;
const mockUseEmblaCarousel = jest.mocked(useEmblaCarousel);

jest.mock('react-aria-components', () => ({
  ...jest.requireActual<typeof ReactAriaComponents>('react-aria-components'),
  TabList: jest.fn(),
}));
const MockBaseTabList = jest.mocked(ReactAriaComponents.TabList);
const ActualBaseTabList =
  jest.requireActual<typeof ReactAriaComponents>('react-aria-components').TabList;

describe('TabList', () => {
  beforeEach(() => {
    mockUseEmblaCarousel.mockReturnValue([emblaRef, emblaApi]);
    MockBaseTabList.mockImplementation((props) => <ActualBaseTabList {...props} />);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders tab list of tabs with carousel and decorative divider', () => {
    mockUseEmblaCarousel.mockReturnValue([
      emblaRef,
      { ...emblaApi, canScrollPrev: jest.fn(() => true), canScrollNext: jest.fn(() => true) },
    ]);

    render(
      <Tabs>
        <TabList label="Tab List" items={items} />
      </Tabs>,
      tenant,
    );

    expect(screen.getByRole('tablist', { name: 'Tab List' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Tab 1' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Tab 2' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'View previous' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'View next' })).toBeInTheDocument();
    expect(screen.getByRole('none')).toBeInTheDocument();
  });

  it('renders TabList with isEqualWidth styles when isEqualWidth is true', () => {
    render(
      <Tabs>
        <Tabs>
          <TabList label="Tab List" items={items} isEqualWidth />
        </Tabs>
      </Tabs>,
    );

    expect(screen.getByRole('tablist', { name: 'Tab List' })).toHaveClass(
      listVariants({ isEqualWidth: true }),
    );
  });

  it('hides decorative divider when hideDivider is true', () => {
    render(
      <Tabs>
        <TabList label="Tab List" items={items} hideDivider />
      </Tabs>,
    );

    expect(screen.queryByRole('none')).not.toBeInTheDocument();
  });

  it.each(['primary', 'secondary'] as const)(
    'renders %s variant carousel when variant is %s',
    (variant) => {
      mockUseEmblaCarousel.mockReturnValue([
        emblaRef,
        { ...emblaApi, canScrollPrev: jest.fn(() => true), canScrollNext: jest.fn(() => true) },
      ]);

      render(
        <Tabs>
          <TabList label="Tab List" items={items} variant={variant} />
        </Tabs>,
        tenant,
      );

      expect(screen.getAllByTestId('arrow-container')[0]).toHaveClass(
        arrowVariants({ type: 'previous', variant }).container(),
      );
      expect(screen.getAllByTestId('arrow-container')[1]).toHaveClass(
        arrowVariants({ type: 'next', variant }).container(),
      );
    },
  );

  it('sets emblaRef to parent element of tab list', () => {
    render(
      <Tabs>
        <TabList label="Tab List" items={items} />
      </Tabs>,
    );

    const { parentElement } = screen.getByRole('tablist');

    expect(emblaRef).toHaveBeenCalledWith(parentElement);
  });

  it('does not set emblaRef to parent element of tab list when tab list does not exist', () => {
    MockBaseTabList.mockReturnValue(null);
    render(
      <Tabs>
        <TabList label="Tab List" items={items} />
      </Tabs>,
    );

    expect(emblaRef).not.toHaveBeenCalled();
  });

  it('calls navigate when clicking a link tab', async () => {
    const user = userEvent.setup();
    const linkItems = [
      { id: '1', title: 'Tab 1', href: '/1' },
      { id: '2', title: 'Tab 2', href: '/2' },
    ];
    const navigate = jest.fn();
    render(
      <Tabs>
        <TabList label="Tab List" items={linkItems} navigate={navigate} />
      </Tabs>,
    );

    await user.click(screen.getByText(linkItems[1]!.title));

    expect(navigate).toHaveBeenCalledWith(linkItems[1]!.href, undefined);
  });

  it('updates emblaApi scroll to focusedGroup without animation when a tab has focus event via arrow key', () => {
    render(
      <Tabs>
        <Tabs.List label="Tab List" items={items} />
      </Tabs>,
      tenant,
    );

    document.dispatchEvent(new KeyboardEvent('keydown', { code: 'ArrowRight' }));
    fireEvent(slideNodes[0]!, new FocusEvent('focus'));

    expect(emblaApi.internalEngine().scrollBody.useDuration).toHaveBeenCalledWith(0);
    expect(emblaApi.internalEngine().scrollTo.index).toHaveBeenCalledWith(0, 0);
  });
});

import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { capitalize, startCase } from 'lodash';

import type { TabListProps } from './Tabs';
import { Tabs } from './Tabs';

const meta: Meta<typeof Tabs> = {
  title: 'Navigation/Tabs',
  component: Tabs,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Tabs>;

const DEFAULT_ITEMS: TabListProps['items'] = [
  {
    id: 'travel',
    title: 'Travel',
  },
  {
    id: 'flight',
    title: 'Flight',
    iconName: 'plane',
  },
  {
    id: 'hotels',
    title: 'Hotels',
    numberBadge: { count: 5, variant: 'neutral' },
  },
  {
    id: 'cars',
    title: 'Car Rental',
    iconName: 'car',
    numberBadge: { count: 5, variant: 'neutral' },
  },
  {
    id: 'cruise',
    title: 'Cruise',
    iconName: 'cruise',
    numberBadge: { count: 15, variant: 'neutral', maxValue: 10 },
  },
  {
    id: 'shopping',
    title: 'Shopping',
    iconName: 'gift',
    numberBadge: { count: 150, variant: 'neutral', maxValue: 100 },
  },
];

export const Primary: Story = {
  render: (args) => (
    <Tabs {...args}>
      <Tabs.List label="Tabs" items={DEFAULT_ITEMS} />
      {DEFAULT_ITEMS.map(({ id, title }) => (
        <Tabs.Panel key={id} id={id}>
          This is the {title} content
        </Tabs.Panel>
      ))}
    </Tabs>
  ),
};

export const Secondary: Story = {
  render: (args) => (
    <Tabs {...args}>
      <Tabs.List label="Tabs" items={DEFAULT_ITEMS} variant="secondary" />
      {DEFAULT_ITEMS.map(({ id, title }) => (
        <Tabs.Panel key={id} id={id}>
          This is the {title} content
        </Tabs.Panel>
      ))}
    </Tabs>
  ),
};

export const HideDivider: Story = {
  render: (args) => (
    <Tabs {...args}>
      <Tabs.List label="Tabs" items={DEFAULT_ITEMS} hideDivider variant="secondary" />
      {DEFAULT_ITEMS.map(({ id, title }) => (
        <Tabs.Panel key={id} id={id} className="container">
          This is the {title} content
        </Tabs.Panel>
      ))}
    </Tabs>
  ),
};

export const LinkTabs: Story = {
  render: function TabsStory(args) {
    const [activeTab, setActiveTab] = useState<string>('travel');

    return (
      <Tabs selectedKey={activeTab} {...args}>
        <Tabs.List
          label="Link Tabs"
          items={DEFAULT_ITEMS.map((item) => ({ ...item, href: item.id }))}
          navigate={(href) => setActiveTab(href)}
        />
        <Tabs.Panel id={activeTab}>
          <div className="border-gray-200 mt-5 rounded border p-4">
            {capitalize(startCase(activeTab))} page
          </div>
        </Tabs.Panel>
      </Tabs>
    );
  },
};

const EQUAL_WIDTH_ITEMS = [
  {
    id: 'short',
    title: 'Short',
  },
  {
    id: 'medium',
    title: 'Medium',
  },
  {
    id: 'long',
    title: 'Much Longer Title',
  },
];

export const EqualWidth: Story = {
  parameters: {
    layout: {
      padded: true,
      width: '800px',
    },
  },
  render: (args) => (
    <Tabs {...args}>
      <Tabs.List isEqualWidth label="Equal Width Tabs" items={EQUAL_WIDTH_ITEMS} />
      {EQUAL_WIDTH_ITEMS.map(({ id, title }) => (
        <Tabs.Panel key={id} id={id}>
          This is the {title} content
        </Tabs.Panel>
      ))}
    </Tabs>
  ),
};

import type { EmblaCarouselType } from 'embla-carousel';
import type { ReactNode } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { axe } from 'jest-axe';

import { fireEvent, render, renderHook, screen, userEvent } from '@qantasexperiences/ui/test-utils';

import { useCarousel } from '../Navigation/providers/CarouselProvider';
import { Tabs } from './Tabs';

const tenant = { brand: 'qantas', channel: 'hotels' } as const;

const element = document.createElement('div');
const slideNodes = [element, element, element, element];

jest.mock('embla-carousel-react');
const emblaRef = jest.fn();
const emblaApi = {
  slideNodes: () => slideNodes,
  canScrollNext: () => true,
  canScrollPrev: () => true,
  scrollNext: jest.fn(),
  scrollPrev: jest.fn(),
  on: jest.fn().mockReturnThis(),
  internalEngine: jest.fn().mockReturnValue({
    options: {},
    slideRegistry: [
      [0, 1],
      [2, 3],
    ],
    scrollBody: { useDuration: jest.fn() },
    scrollTo: { index: jest.fn() },
  }),
} as unknown as EmblaCarouselType;
const mockUseEmblaCarousel = jest.mocked(useEmblaCarousel);

const items = [
  { id: '1', title: 'Tab 1' },
  { id: '2', title: 'Tab 2', iconName: 'bed' as const },
  {
    id: '3',
    title: 'Tab 3',
    numberBadge: { count: 5, variant: 'neutral' } as const,
  },
  {
    id: '4',
    title: 'Tab 4',
    iconName: 'bed' as const,
    numberBadge: { count: 5, variant: 'neutral' } as const,
  },
];

const tabListProps = {
  label: 'Tabs',
  items,
};

describe('Tabs', () => {
  beforeEach(() => {
    mockUseEmblaCarousel.mockReturnValue([emblaRef, emblaApi]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders children', () => {
    render(<Tabs>children</Tabs>);

    expect(screen.getAllByText('children')).toBeTruthy();
  });

  it('provides emblaApi and emblaRef to children', () => {
    const { result } = renderHook(() => useCarousel(), {
      wrapper: ({ children }: { children: ReactNode }) => <Tabs>{children}</Tabs>,
    });

    expect(result.current).toEqual({
      emblaApi,
      emblaRef,
    });
  });

  it('renders tabs', () => {
    render(
      <Tabs>
        <Tabs.List label="Tabs" items={items} />
        {items.map(({ id }) => (
          <Tabs.Panel key={id} id={id}>
            Panel {id}
          </Tabs.Panel>
        ))}
      </Tabs>,
      tenant,
    );

    expect(screen.getByRole('tablist', { name: tabListProps.label })).toBeInTheDocument();
    expect(screen.getAllByRole('tab')).toHaveLength(items.length);

    items.forEach((item) => {
      expect(screen.getByText(item.title)).toBeInTheDocument();
    });

    expect(screen.getAllByText('5')).toHaveLength(2);
    expect(screen.getAllByTestId('bed-icon')).toHaveLength(2);
    expect(screen.getByText('Panel 1')).toBeInTheDocument();
  });

  it('switches panels when clicking a tab', async () => {
    const user = userEvent.setup();
    render(
      <Tabs>
        <Tabs.List label="Tabs" items={items} />
        {items.map(({ id }) => (
          <Tabs.Panel key={id} id={id}>
            Panel {id}
          </Tabs.Panel>
        ))}
      </Tabs>,
      tenant,
    );

    expect(screen.getByText('Panel 1')).toBeInTheDocument();
    expect(screen.queryByText('Panel 2')).not.toBeInTheDocument();

    await user.click(screen.getByText('Tab 2'));

    expect(screen.queryByText('Panel 1')).not.toBeInTheDocument();
    expect(screen.getByText('Panel 2')).toBeInTheDocument();
  });

  it('switches panels via manual keyboard activation for tabs', async () => {
    const user = userEvent.setup();
    render(
      <Tabs>
        <Tabs.List label="Tabs" items={items} />
        {items.map(({ id }) => (
          <Tabs.Panel key={id} id={id}>
            Panel {id}
          </Tabs.Panel>
        ))}
      </Tabs>,
      tenant,
    );

    await user.tab();
    await user.keyboard('{ArrowRight}');

    expect(screen.getByText('Panel 1')).toBeInTheDocument();
    expect(screen.queryByText('Panel 2')).not.toBeInTheDocument();

    await user.keyboard('{Enter}');

    expect(screen.queryByText('Panel 1')).not.toBeInTheDocument();
    expect(screen.getByText('Panel 2')).toBeInTheDocument();
  });

  it('renders disabled tabs when tabs is dragging', () => {
    render(
      <Tabs data-testid="tabs">
        <Tabs.List label="Tabs" items={items} />
      </Tabs>,
      tenant,
    );

    const tabs = screen.getByTestId('tabs');

    fireEvent(tabs, new TouchEvent('touchmove'));

    screen.getAllByRole('tab').forEach((tab) => {
      expect(tab).toHaveAttribute('aria-disabled', 'true');
      expect(tab).toHaveAttribute('data-disabled', 'true');
    });

    fireEvent(tabs, new TouchEvent('touchend'));

    screen.getAllByRole('tab').forEach((tab) => {
      expect(tab).not.toHaveAttribute('aria-disabled', 'true');
      expect(tab).not.toHaveAttribute('data-disabled', 'true');
    });
  });

  it('updates emblaApi scroll to focusedGroup without animation when tab has focus event', () => {
    render(
      <Tabs>
        <Tabs.List label="Tabs" items={items} />
      </Tabs>,
      tenant,
    );

    document.dispatchEvent(new KeyboardEvent('keydown', { code: 'ArrowRight' }));
    fireEvent(slideNodes[0]!, new FocusEvent('focus'));

    expect(emblaApi.internalEngine().scrollBody.useDuration).toHaveBeenCalledWith(0);
    expect(emblaApi.internalEngine().scrollTo.index).toHaveBeenCalledWith(0, 0);
  });

  it('renders with custom prop and additional class name', () => {
    render(
      <Tabs data-testid="tabs" className="bg-secondary">
        children
      </Tabs>,
      tenant,
    );

    expect(screen.getByTestId('tabs')).toHaveClass('w-full bg-secondary');
  });

  it('does not have accessibility violations', async () => {
    const { container } = render(
      <Tabs>
        <Tabs.List label="Tabs" items={items} />
        {items.map(({ id }) => (
          <Tabs.Panel key={id} id={id}>
            Panel {id}
          </Tabs.Panel>
        ))}
      </Tabs>,
      tenant,
    );

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});

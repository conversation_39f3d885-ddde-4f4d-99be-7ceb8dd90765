import type { UserEvent } from '@testing-library/user-event';
import { forwardRef } from 'react';
import { Button } from 'react-aria-components';

import { fireEvent, render, screen, userEvent } from '@qantasexperiences/ui/test-utils';

import type { TooltipProps } from './Tooltip';
import { Tooltip } from './Tooltip';

const TestTooltip = ({ children = 'trigger', ...props }: TooltipProps) => {
  return (
    <Tooltip tooltipContent={<span data-testid="tooltip-test-content">content</span>} {...props}>
      {children}
    </Tooltip>
  );
};

const renderTooltip = (props?: TooltipProps) =>
  render(<TestTooltip {...props} />, { brand: 'qantas', channel: 'holidays' });
const getTooltipContent = () => screen.queryByTestId('tooltip-test-content');
const getTooltipTrigger = () => screen.getByRole('button', { name: 'trigger' });

describe('Tooltip', () => {
  let user: UserEvent;

  beforeEach(() => {
    user = userEvent.setup();
  });

  it('shows on hover, hides on unhover', async () => {
    renderTooltip();

    expect(getTooltipContent()).not.toBeInTheDocument();

    // mouseMove is required for react-aria to pick up mouse hover event
    fireEvent.mouseMove(document.body);
    await user.hover(getTooltipTrigger());

    expect(getTooltipContent()).toBeInTheDocument();

    await user.unhover(getTooltipTrigger());

    expect(getTooltipContent()).not.toBeInTheDocument();
  });

  it('shows on focus, hides on blur', async () => {
    renderTooltip();

    expect(getTooltipContent()).not.toBeInTheDocument();

    await user.tab();

    expect(getTooltipContent()).toBeInTheDocument();

    await user.tab();

    expect(getTooltipContent()).not.toBeInTheDocument();
  });

  it('shows on click, hides on click away', async () => {
    renderTooltip();

    expect(getTooltipContent()).not.toBeInTheDocument();

    await user.click(getTooltipTrigger());

    expect(getTooltipContent()).toBeInTheDocument();

    await user.click(document.body);

    expect(getTooltipContent()).not.toBeInTheDocument();
  });

  describe('arrow', () => {
    const getArrow = () => screen.queryByTestId('tooltip-arrow');

    it('shows arrow by default or when includeArrow prop is true', async () => {
      const { rerender } = render(<TestTooltip includeArrow />);

      await user.tab();

      expect(getArrow()).toBeInTheDocument();

      rerender(<TestTooltip />);

      expect(getArrow()).toBeInTheDocument();
    });

    it('hides arrow when includeArrow prop is false', async () => {
      render(<TestTooltip includeArrow={false} />);

      await user.tab();

      expect(getArrow()).not.toBeInTheDocument();
    });
  });

  it('supports being controlled externally', async () => {
    const { rerender } = render(<TestTooltip isOpen />);

    expect(getTooltipContent()).toBeInTheDocument();

    fireEvent.mouseMove(document.body);
    await user.hover(getTooltipTrigger());
    await user.unhover(getTooltipTrigger());

    expect(getTooltipTrigger()).toBeInTheDocument();

    rerender(<TestTooltip isOpen={false} />);

    expect(getTooltipContent()).not.toBeInTheDocument();

    await user.hover(getTooltipTrigger());

    expect(getTooltipContent()).not.toBeInTheDocument();

    await user.click(getTooltipTrigger());

    expect(getTooltipContent()).not.toBeInTheDocument();
  });

  describe('dismiss button', () => {
    const getDismissButton = () => screen.queryByRole('button', { name: 'close' });

    it('shows dismiss button only when isDismissible is true', async () => {
      const { rerender } = renderTooltip();

      await user.tab();

      expect(getDismissButton()).not.toBeInTheDocument();

      rerender(<TestTooltip isDismissible />);

      expect(getDismissButton()).toBeInTheDocument();
    });

    it('calls onDismiss when dismiss button is clicked', async () => {
      const onDismiss = jest.fn();
      render(<TestTooltip isDismissible onDismiss={onDismiss} isOpen />, {
        channel: 'holidays',
        brand: 'jetstar',
      });

      await user.tab();

      expect(onDismiss).not.toHaveBeenCalled();

      // @ts-expect-error test should fail if null
      await user.click(getDismissButton());

      expect(onDismiss).toHaveBeenCalled();
    });
  });

  describe('asChild', () => {
    const getAsChildElement = () => screen.getByTestId('as-child-test');

    it('supports a base html element with asChild', async () => {
      render(
        <TestTooltip asChild>
          <span data-testid="as-child-test">test as child</span>
        </TestTooltip>,
      );

      fireEvent.mouseMove(document.body);
      await user.hover(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.unhover(getAsChildElement());

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.click(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.click(document.body);

      expect(getTooltipContent()).not.toBeInTheDocument();
    });

    it('supports a custom component with asChild', async () => {
      /* eslint-disable */
      const CustomComponent = forwardRef((props, ref) => {
        return (
          // @ts-ignore
          <div data-testid="as-child-test" {...props} ref={ref}>
            test as child
          </div>
        );
      });
      /* eslint-enable */

      render(
        <TestTooltip asChild>
          <CustomComponent />
        </TestTooltip>,
      );

      fireEvent.mouseMove(document.body);
      await user.hover(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.unhover(getAsChildElement());

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.click(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.click(document.body);

      expect(getTooltipContent()).not.toBeInTheDocument();
    });

    it('supports react-aria Button with asChild', async () => {
      render(
        <TestTooltip asChild>
          <Button data-testid="as-child-test" />
        </TestTooltip>,
      );

      fireEvent.mouseMove(document.body);
      await user.hover(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.unhover(getAsChildElement());

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.click(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.click(document.body);

      expect(getTooltipContent()).not.toBeInTheDocument();
    });
  });
});

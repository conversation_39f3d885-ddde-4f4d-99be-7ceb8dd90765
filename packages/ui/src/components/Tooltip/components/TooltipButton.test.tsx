import type { UserEvent } from '@testing-library/user-event';
import { forwardRef } from 'react';
import { Button, TooltipTrigger } from 'react-aria-components';

import { fireEvent, render, screen, userEvent } from '@qantasexperiences/ui/test-utils';

import type { TooltipButtonProps } from './TooltipButton';
import { TooltipButton } from './TooltipButton';
import { TooltipContent } from './TooltipContent';

const TestTooltipButton = ({ children = 'trigger', ...props }: TooltipButtonProps) => {
  return (
    <TooltipTrigger delay={0} closeDelay={0}>
      <TooltipButton {...props}>{children}</TooltipButton>
      <TooltipContent>
        <span data-testid="tooltip-test-content">content</span>
      </TooltipContent>
    </TooltipTrigger>
  );
};

const renderTooltipButton = (props?: TooltipButtonProps) =>
  render(<TestTooltipButton {...props} />);
const getTooltipContent = () => screen.queryByTestId('tooltip-test-content');
const getTooltipTrigger = () => screen.getByRole('button', { name: 'trigger' });

describe('TooltipButton', () => {
  let user: UserEvent;

  beforeEach(() => {
    user = userEvent.setup();
  });

  it('shows tooltip on hover, hides on unhover', async () => {
    renderTooltipButton();

    expect(getTooltipContent()).not.toBeInTheDocument();

    // mouseMove is required for react-aria to pick up mouse hover event
    fireEvent.mouseMove(document.body);
    await user.hover(getTooltipTrigger());

    expect(getTooltipContent()).toBeInTheDocument();

    await user.unhover(getTooltipTrigger());

    expect(getTooltipContent()).not.toBeInTheDocument();
  });

  it('shows tooltip on focus, hides on blur', async () => {
    renderTooltipButton();

    expect(getTooltipContent()).not.toBeInTheDocument();

    await user.tab();

    expect(getTooltipContent()).toBeInTheDocument();

    await user.tab();

    expect(getTooltipContent()).not.toBeInTheDocument();
  });

  it('shows tooltip on click, hides on click away', async () => {
    renderTooltipButton();

    expect(getTooltipContent()).not.toBeInTheDocument();

    await user.click(getTooltipTrigger());

    expect(getTooltipContent()).toBeInTheDocument();

    await user.click(document.body);

    expect(getTooltipContent()).not.toBeInTheDocument();
  });

  it('renders tooltip child as a span instead of a button when tooltip is disabled', async () => {
    renderTooltipButton({ isDisabled: true });

    expect(getTooltipContent()).not.toBeInTheDocument();

    // Intentionally using getByText as there are no interaction roles
    await user.click(screen.getByText('trigger'));

    expect(getTooltipContent()).not.toBeInTheDocument();
  });

  describe('asChild', () => {
    const getAsChildElement = () => screen.getByTestId('as-child-test');

    it('supports a base html element with asChild', async () => {
      render(
        <TestTooltipButton asChild>
          <span data-testid="as-child-test">test as child</span>
        </TestTooltipButton>,
      );

      fireEvent.mouseMove(document.body);
      await user.hover(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.unhover(getAsChildElement());

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.click(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.click(document.body);

      expect(getTooltipContent()).not.toBeInTheDocument();
    });

    it('supports a custom component with asChild', async () => {
      /* eslint-disable */
      const CustomComponent = forwardRef((props, ref) => {
        return (
          // @ts-ignore
          <div data-testid="as-child-test" {...props} ref={ref}>
            test as child
          </div>
        );
      });
      /* eslint-enable */

      render(
        <TestTooltipButton asChild>
          <CustomComponent />
        </TestTooltipButton>,
      );

      fireEvent.mouseMove(document.body);
      await user.hover(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.unhover(getAsChildElement());

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.click(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.click(document.body);

      expect(getTooltipContent()).not.toBeInTheDocument();
    });

    it('supports react-aria Button with asChild', async () => {
      render(
        <TestTooltipButton asChild>
          <Button data-testid="as-child-test" />
        </TestTooltipButton>,
      );

      fireEvent.mouseMove(document.body);
      await user.hover(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.unhover(getAsChildElement());

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).toBeInTheDocument();

      await user.tab();

      expect(getTooltipContent()).not.toBeInTheDocument();

      await user.click(getAsChildElement());

      expect(getTooltipContent()).toBeInTheDocument();

      await user.click(document.body);

      expect(getTooltipContent()).not.toBeInTheDocument();
    });
  });
});

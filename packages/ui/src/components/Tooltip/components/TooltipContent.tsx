'use client';

import type { ComponentProps, ForwardedRef, MouseEventHandler, ReactNode } from 'react';
import type { TooltipProps as ReactAriaTooltipProps } from 'react-aria-components';
import { forwardRef, useContext } from 'react';
import { Tooltip as ReactAriaTooltip, TooltipTriggerStateContext } from 'react-aria-components';

import { cn } from '@qantasexperiences/utils/tailwind';

import type { TooltipVariant } from '../types';
import { SvgIcon } from '../../SvgIcon';

export interface TooltipContentProps extends ReactAriaTooltipProps {
  children?: ReactNode;
  isDismissible?: boolean;
  onDismiss?: () => void;
  variant?: TooltipVariant;
}

const DismissButton = ({ className, onClick, ...props }: ComponentProps<'button'>) => {
  const state = useContext(TooltipTriggerStateContext);

  const handleClick: MouseEventHandler<HTMLButtonElement> = (event) => {
    state?.close(true);
    onClick?.(event);
  };

  return (
    <button
      className={cn('absolute right-1 top-1 p-1 !outline-none', className)}
      aria-label="close"
      onClick={handleClick}
      {...props}
    >
      <SvgIcon name={'cross-small'} className="h-6 w-6" />
    </button>
  );
};

const TooltipContent = (
  {
    children,
    className,
    offset = 12,
    variant = 'primary',
    isDismissible,
    onDismiss,
    ...rest
  }: TooltipContentProps,
  forwardedRef: ForwardedRef<HTMLDivElement>,
) => {
  return (
    <ReactAriaTooltip
      offset={offset}
      className={cn(
        variant === 'secondary' && 'bg-notification-info-subtle text-text-primary',
        variant === 'primary' && 'bg-surface-background-toast text-text-primary-on-dark',
        'relative !z-auto max-w-80 rounded-lg px-4 py-2 text-body-md font-normal',
        isDismissible && 'pr-10',
        className,
      )}
      ref={forwardedRef}
      {...rest}
    >
      {children}
      {isDismissible && <DismissButton onClick={() => onDismiss?.()} />}
    </ReactAriaTooltip>
  );
};

const ForwardedTooltipContent = forwardRef(TooltipContent);
export { ForwardedTooltipContent as TooltipContent };

import type { Meta, StoryObj } from '@storybook/react';
import { useArgs } from '@storybook/preview-api';

import type { TooltipProps } from './Tooltip';
import { Tooltip } from './Tooltip';

const Render = (props: TooltipProps) => {
  return <Tooltip className="m-10 h-20 w-20 bg-button-primary-enabled text-white" {...props} />;
};

const meta: Meta<typeof Tooltip> = {
  title: 'Overlays/Tooltip',
  component: Tooltip,
  parameters: {
    layout: 'centered',
  },
  args: {
    tooltipContent: 'tooltip content',
    children: 'trigger content',
  },
  render: function RenderFn(args) {
    const [{ isOpen }, updateArgs] = useArgs();

    return (
      <Render
        isOpen={isOpen as boolean}
        onDismiss={() => updateArgs({ isOpen: false })}
        {...args}
      />
    );
  },
};

export default meta;

type Story = StoryObj<typeof Tooltip>;

export const Primary: Story = {
  args: { variant: 'primary' },
};

export const Secondary: Story = {
  args: { variant: 'secondary' },
};

export const Dismissible: Story = {
  args: { isDismissible: true, isOpen: true },
};

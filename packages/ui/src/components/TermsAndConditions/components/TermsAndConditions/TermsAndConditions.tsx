'use client';

import { useId } from 'react';

import type { LegalDisclaimer } from '../../types';
import { useTermsAndConditionsStore } from '../../store/useTermsAndConditionsStore';
import { getDisclaimerId } from '../../utils';
import { TermsAndConditionsItem } from './components/TermsAndConditionsItem';

export const TermsAndConditions = () => {
  const id = useId();
  const disclaimers = useTermsAndConditionsStore();

  if (disclaimers.size === 0) return null;

  const uniqueDisclaimers = [...disclaimers.values()].reduce((accumulator, current) => {
    if (!accumulator.has(current.key ?? current.id))
      accumulator.set(current.key ?? current.id, current);
    return accumulator;
  }, new Map<string, LegalDisclaimer>());

  return (
    <section className="container my-6 md:my-8 lg:my-12" aria-labelledby={id}>
      <h3 className="text-lg leading-7 j:font-bold q:font-medium" id={id}>
        Important information
      </h3>
      <ul className="mt-[1lh] space-y-[1lh] text-body-sm text-text-secondary [&_a]:underline j:[&_a]:text-interactive-secondary-enabled">
        {Array.from(uniqueDisclaimers.values()).map((disclaimer) => (
          <li key={disclaimer.id} id={getDisclaimerId(disclaimer.key)} className="scroll-mt-[1lh]">
            <TermsAndConditionsItem disclaimer={disclaimer} />
          </li>
        ))}
      </ul>
    </section>
  );
};

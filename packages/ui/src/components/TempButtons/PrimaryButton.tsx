'use client';

import type { ReactNode } from 'react';
import React from 'react';
import { But<PERSON> } from 'react-aria-components';

import type { ClassValue } from '@qantasexperiences/utils/tailwind';
import { cn } from '@qantasexperiences/utils/tailwind';

interface PrimaryButtonProps {
  children: ReactNode;
  className?: ClassValue;
  fullWidth?: boolean;
  onPress?: () => void;
  slot?: string;
  variant?: 'primary' | 'secondary';
}

// Please substitute this button for a designed component button
export const PrimaryButton = ({
  children,
  className,
  variant = 'primary',
  fullWidth,
  ...props
}: PrimaryButtonProps) => (
  <Button
    className={cn(
      // Common Base
      'h-12 text-body-md font-bold transition duration-300',
      variant === 'primary' && 'bg-button-primary-enabled text-white',
      variant === 'secondary' &&
        'border-2 border-button-primary-enabled text-button-primary-enabled',
      // Brand-Specific Base
      fullWidth ? 'w-full' : 'j:w-[195px] q:w-[180px]',
      'q:rounded-md q:uppercase',
      variant === 'primary' && 'j:w-[195px] j:rounded-full',
      variant === 'secondary' && 'j:rounded-md',
      // Hover
      variant === 'primary' && 'hover:bg-button-primary-hover',
      // Focus
      'focus:outline-0',
      'focus-visible:outline-2 focus-visible:outline-input-highlight-outline',
      className,
    )}
    {...props}
  >
    {children}
  </Button>
);

'use client';

import React from 'react';
import { ColumnResizer, Column as TableColumn } from 'react-aria-components';

import { cn } from '@qantasexperiences/utils/tailwind';

import type { ICON_MAP } from '../../../SvgIcon/icon-map';
import type { ColumnType } from '../../Table';
import { SvgIcon } from '../../../SvgIcon';
import { Tooltip } from '../../../Tooltip';

export const Column = ({
  UNSTABLE_tooltipPortalContainer,
  children,
  className,
  maxWidth,
  minWidth = 100,
  id,
  isRowHeader,
  isResizable,
  isSortable,
  isSticky,
  sortDirection,
  tooltip,
}: ColumnType) => {
  let sortIcon: keyof typeof ICON_MAP = 'sort-table-vertical';
  if (sortDirection === 'ascending') sortIcon = 'arrow-up';
  else if (sortDirection === 'descending') sortIcon = 'arrow-down';

  return (
    <TableColumn
      id={id}
      className={cn(
        'top-0 border-y border-divider-subtle bg-surface-background-secondary pl-3 first:pl-8 last:pr-8',
        isSticky && 'sticky',
        !isResizable && 'pr-3',
        isSortable && 'cursor-pointer hover:bg-surface-background-tertiary',
        className,
      )}
      maxWidth={maxWidth}
      minWidth={minWidth}
      isRowHeader={isRowHeader}
      allowsSorting={isSortable}
    >
      <div className="flex items-center">
        <div className="shrink-0 py-4">{children}</div>
        {isSortable && (
          <SvgIcon
            name={sortIcon}
            data-testid="sort-table-icon"
            className={cn(
              'ml-1 inline size-5 text-text-secondary',
              sortDirection ? 'text-brand-primary' : 'text-neutral-500',
            )}
          />
        )}
        {!!tooltip && (
          <Tooltip
            UNSTABLE_portalContainer={UNSTABLE_tooltipPortalContainer}
            tooltipContent={tooltip}
            className="flex pr-1"
          >
            <SvgIcon
              aria-label="More info"
              name="info-circle"
              className="ml-1 size-4 text-text-secondary"
            />
          </Tooltip>
        )}
        {isResizable && (
          <div className="grow self-stretch">
            <ColumnResizer
              className="group float-right h-full cursor-col-resize focus-within:ring-2"
              aria-label="Resize column"
            >
              <div className="mx-2 my-4 h-6 w-px rounded bg-divider-standard group-hover:bg-divider-strong group-active:bg-divider-strong" />
            </ColumnResizer>
          </div>
        )}
      </div>
    </TableColumn>
  );
};

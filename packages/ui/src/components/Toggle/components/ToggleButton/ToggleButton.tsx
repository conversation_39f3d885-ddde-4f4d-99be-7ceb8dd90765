'use client';

import type { ForwardedRef } from 'react';
import type { RadioProps } from 'react-aria-components';
import { forwardRef } from 'react';
import { Radio } from 'react-aria-components';

import { cn } from '@qantasexperiences/utils/tailwind';

export type ToggleButtonProps = RadioProps;

const ToggleButton = (
  { children, className, ...rest }: ToggleButtonProps,
  ref: ForwardedRef<HTMLLabelElement>,
) => {
  return (
    <Radio
      className={cn(
        // Default
        'h-14 border border-toggle-border bg-surface-background-primary px-6 py-1 text-body-md text-text-primary',
        'flex cursor-pointer items-center justify-center transition duration-200',
        // Selected
        'rac-selected:border-interactive-primary-enabled rac-selected:text-interactive-primary-enabled',
        'rac-selected:z-10',
        // Disabled
        'rac-disabled:cursor-not-allowed rac-disabled:border-toggle-border rac-disabled:text-text-disabled',
        'rac-disabled:bg-disabled-background-color',
        // Hover
        'hover:[&:not([data-selected]):not([data-disabled])]:bg-surface-background-secondary',
        // Focus
        'rac-focus-visible:outline rac-focus-visible:outline-[3px] rac-focus-visible:outline-input-highlight-outline',
        'rac-focus-visible:-outline-offset-[2px]',
        className,
      )}
      ref={ref}
      {...rest}
    >
      {children}
    </Radio>
  );
};

const ForwardedToggleButton = forwardRef(ToggleButton);

export { ForwardedToggleButton as ToggleButton };

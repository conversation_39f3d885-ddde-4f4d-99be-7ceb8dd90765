import type { FocusEvent } from 'react';
import { useRef } from 'react';

const isEventBubbledFromPortal = (event: FocusEvent<HTMLElement, Element>) => {
  return !event.currentTarget.contains(event.target);
};

const isNewFocusedElementWithin = (event: FocusEvent<HTMLElement, Element>) => {
  return event.currentTarget.contains(event.relatedTarget);
};

export interface UseFocusWithinProps {
  /** Whether to consider elements that are rendered through a portal but are descendants in the React tree
   * as being 'within' */
  includePortalDescendants?: boolean;
  onBlurWithin?: (e: FocusEvent<HTMLElement, Element>) => void;
  onFocusWithin?: (e: FocusEvent<HTMLElement, Element>) => void;
  onFocusWithinChange?: (focus: boolean) => void;
}

export const useFocusWithin = (options?: UseFocusWithinProps) => {
  const { includePortalDescendants, onFocusWithin, onBlurWithin, onFocusWithinChange } =
    options ?? {};
  const state = useRef({ isFocusWithin: false });

  const onFocus = (e: FocusEvent<HTMLElement, Element>) => {
    if (state.current.isFocusWithin) {
      return;
    }

    if (isEventBubbledFromPortal(e) && !includePortalDescendants) {
      return;
    }

    state.current.isFocusWithin = true;
    onFocusWithinChange?.(true);
    onFocusWithin?.(e);
  };

  const onBlur = (e: FocusEvent<HTMLElement, Element>) => {
    if (!state.current.isFocusWithin) {
      return;
    }

    if (isEventBubbledFromPortal(e) && !includePortalDescendants) {
      return;
    }

    if (isNewFocusedElementWithin(e)) {
      return;
    }

    state.current.isFocusWithin = false;
    onFocusWithinChange?.(false);
    onBlurWithin?.(e);
  };

  return {
    focusWithinProps: {
      onFocus,
      onBlur,
    },
  };
};

import { act, renderHook } from '@qantasexperiences/ui/test-utils';

import { useSearchParams } from './useSearchParams';

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams('foo=bar&baz=qux')),
}));

describe('useSearchParams', () => {
  let replaceStateSpy: jest.SpyInstance;
  const { location } = window;

  beforeAll(() => {
    replaceStateSpy = jest.spyOn(window.history, 'replaceState');
    Object.defineProperty(window, 'location', {
      value: { search: 'foo=bar&baz=qux' },
      writable: true,
    });
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    replaceStateSpy.mockRestore();
    window.location = location;
  });

  it('passes through searchParams value directly from next/navigation useSearchParams', () => {
    const { result } = renderHook(() => useSearchParams());

    expect(result.current.searchParams.toString()).toBe('foo=bar&baz=qux');
  });

  it('correctly adds new search params without removing existing search params', () => {
    const { result } = renderHook(() => useSearchParams());
    act(() => result.current.updateSearchParams({ test: 'value' }));

    expect(replaceStateSpy).toHaveBeenCalledWith(null, '', '?foo=bar&baz=qux&test=value');
  });

  it('correctly clears existing search params if reset option is true', () => {
    const { result } = renderHook(() => useSearchParams());
    act(() => result.current.updateSearchParams({ test: 'value' }, { reset: true }));

    expect(replaceStateSpy).toHaveBeenCalledWith(null, '', '?test=value');
  });

  it('correctly updates search params', () => {
    const { result } = renderHook(() => useSearchParams());
    act(() => result.current.updateSearchParams({ test: 'value', foo: 'not-bar' }));

    expect(replaceStateSpy).toHaveBeenCalledWith(null, '', '?foo=not-bar&baz=qux&test=value');
  });

  it('correctly clears search params when provided with a null or undefined value', () => {
    const { result } = renderHook(() => useSearchParams());
    act(() => result.current.updateSearchParams({ test: 'value', foo: null }));

    expect(replaceStateSpy).toHaveBeenLastCalledWith(null, '', '?baz=qux&test=value');

    act(() => result.current.updateSearchParams({ test: 'value', baz: undefined }));

    expect(replaceStateSpy).toHaveBeenLastCalledWith(null, '', '?foo=bar&test=value');
  });
});
